import React, { useState, useEffect } from 'react';
import { Modal, Table, Button, Form, Input, Space, Popconfirm, Tag, App } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { TaskBasicGroupFormData } from '../types';
import { TaskService } from '../services';
// import { useTaskData } from '../hooks';

interface GroupManagementModalProps {
  visible: boolean;
  onCancel: () => void;
}

/**
 * 分组管理Modal组件
 */
const GroupManagementModal: React.FC<GroupManagementModalProps> = ({ visible, onCancel }) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  // 状态管理
  const [groups, setGroups] = useState<TaskBasicGroupFormData[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingGroup, setEditingGroup] = useState<TaskBasicGroupFormData | null>(null);
  const [addModalVisible, setAddModalVisible] = useState(false);

  // 加载分组数据
  const loadGroups = async () => {
    setLoading(true);
    try {
      const response = await TaskService.getTaskGroups({
        current: 1,
        page_size: 1000, // 加载所有分组
      });
      setGroups(response.data);
    } catch (error) {
      console.error('加载分组失败:', error);
      message.error('加载分组失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    if (visible) {
      loadGroups();
    }
  }, [visible]);

  // 新增/编辑分组
  const handleSubmit = async (values: any) => {
    try {
      if (editingGroup) {
        // 编辑模式
        console.log('更新分组:', { ...editingGroup, ...values });
        message.success('更新分组成功');
      } else {
        // 新增模式
        console.log('新增分组:', values);
        message.success('新增分组成功');
      }

      setAddModalVisible(false);
      setEditingGroup(null);
      form.resetFields();
      loadGroups();
    } catch (error) {
      console.error('保存分组失败:', error);
      message.error('保存分组失败');
    }
  };

  // 删除分组
  const handleDelete = async (id: number) => {
    try {
      console.log('删除分组:', id);
      message.success('删除分组成功');
      loadGroups();
    } catch (error) {
      console.error('删除分组失败:', error);
      message.error('删除分组失败');
    }
  };

  // 表格列配置
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '分组名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '使用状态',
      dataIndex: 'is_used',
      key: 'is_used',
      width: 100,
      render: (is_used: boolean) => <Tag color={is_used ? 'success' : 'default'}>{is_used ? '已使用' : '未使用'}</Tag>,
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 180,
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: string, record: TaskBasicGroupFormData) => (
        <Space size='small'>
          <Button
            type='text'
            size='small'
            icon={<EditOutlined />}
            onClick={() => {
              setEditingGroup(record);
              form.setFieldsValue(record);
              setAddModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm title='确认删除' description='确定要删除这个分组吗？' onConfirm={() => handleDelete(record.id)} okText='确定' cancelText='取消'>
            <Button type='text' size='small' danger icon={<DeleteOutlined />} disabled={record.is_used}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Modal
        title='分组管理'
        open={visible}
        onCancel={onCancel}
        width={800}
        footer={
          <div className='flex justify-between'>
            <Button
              type='primary'
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingGroup(null);
                form.resetFields();
                setAddModalVisible(true);
              }}
            >
              新增分组
            </Button>
            <Button onClick={onCancel}>关闭</Button>
          </div>
        }
      >
        <Table
          columns={columns}
          dataSource={groups}
          rowKey='id'
          loading={loading}
          size='small'
          scroll={{ y: 400 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showQuickJumper: true,
            showTotal: total => `共 ${total} 条`,
          }}
        />
      </Modal>

      {/* 新增/编辑分组Modal */}
      <Modal
        title={editingGroup ? '编辑分组' : '新增分组'}
        open={addModalVisible}
        onCancel={() => {
          setAddModalVisible(false);
          setEditingGroup(null);
          form.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form form={form} layout='vertical' onFinish={handleSubmit}>
          <Form.Item
            label='分组名称'
            name='name'
            rules={[
              { required: true, message: '请输入分组名称' },
              { max: 50, message: '分组名称不能超过50个字符' },
            ]}
          >
            <Input placeholder='请输入分组名称' />
          </Form.Item>

          <div className='flex justify-end gap-3 pt-4 border-t border-gray-200'>
            <Button
              onClick={() => {
                setAddModalVisible(false);
                setEditingGroup(null);
                form.resetFields();
              }}
            >
              取消
            </Button>
            <Button type='primary' htmlType='submit'>
              {editingGroup ? '更新' : '创建'}
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  );
};

export default GroupManagementModal;
